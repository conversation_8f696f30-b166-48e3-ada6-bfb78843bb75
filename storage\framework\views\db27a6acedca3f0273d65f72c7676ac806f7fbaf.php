<style>
    .menu-link:hover {
        background-color: #931F26 !important;
    }

    .menu-link:hover > .menu-text:hover {
        color: #fff !important;
    }

    .menu-item.menu-item-submenu.menu-item-open > .menu-link,
    .menu-item.menu-item-active > .menu-link {
        background-color: #fff !important;
    }

    .menu-item.menu-item-submenu.menu-item-open > .menu-link > .menu-text,
    .menu-item.menu-item-active > .menu-link > .menu-text {
        color: #000000 !important;
    }

    .menu-item.menu-item-submenu.menu-item-open > .menu-link:hover,
    .menu-item.menu-item-active > .menu-link:hover {
        background-color: #931F26 !important;
    }
    .menu-item:hover.menu-item-active > .menu-link > .menu-text:hover {
        color: #FFFFFF !important;
    }

</style>

<div class="aside aside-left aside-fixed d-flex flex-column flex-row-auto" id="kt_sidebar" style='left:-100%;background-color:#fff !important;z-index:9999'>
    <div style="border-radius: 18px;background-color:#fff !important;margin:10px;background: #727272; height: 100%;position:relative">
        <!--begin::Brand-->
        <button id='btn-close' style="position: absolute;right: 0;border-color: transparent;background: transparent;">
            <i class="fa fa-times" aria-hidden="true"></i>
        </button>
        <div class="brand d-block" style="padding: 0 10px">
        <div class="brand flex-column-auto" id="kt_brand" style="width:100% !important;padding: 0;display: flex;">
            <!--begin::Logo-->
            <div class="text-center" style="width:100% !important">
                <a href="<?php echo e(URL::to('/')); ?>" class="brand-logo">
                    <img alt="Logo" src="../assets/media/logos/SIG-LightLogo.png" style="width: 100px;margin: auto;height:55px;"/>
                </a>
            </div>
            <!--end::Logo-->
            <!--begin::Toggle-->
            <!-- <button class="brand-toggle btn btn-sm px-0" id="kt_aside_toggle">
                <span class="svg-icon svg-icon svg-icon-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24" />
                            <path d="M5.29288961,6.70710318 C4.90236532,6.31657888 4.90236532,5.68341391 5.29288961,5.29288961 C5.68341391,4.90236532 6.31657888,4.90236532 6.70710318,5.29288961 L12.7071032,11.2928896 C13.0856821,11.6714686 13.0989277,12.281055 12.7371505,12.675721 L7.23715054,18.675721 C6.86395813,19.08284 6.23139076,19.1103429 5.82427177,18.7371505 C5.41715278,18.3639581 5.38964985,17.7313908 5.76284226,17.3242718 L10.6158586,12.0300721 L5.29288961,6.70710318 Z" fill="#000000" fill-rule="nonzero" transform="translate(8.999997, 11.999999) scale(-1, 1) translate(-8.999997, -11.999999)" />
                            <path d="M10.7071009,15.7071068 C10.3165766,16.0976311 9.68341162,16.0976311 9.29288733,15.7071068 C8.90236304,15.3165825 8.90236304,14.6834175 9.29288733,14.2928932 L15.2928873,8.29289322 C15.6714663,7.91431428 16.2810527,7.90106866 16.6757187,8.26284586 L22.6757187,13.7628459 C23.0828377,14.1360383 23.1103407,14.7686056 22.7371482,15.1757246 C22.3639558,15.5828436 21.7313885,15.6103465 21.3242695,15.2371541 L16.0300699,10.3841378 L10.7071009,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(15.999997, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-15.999997, -11.999999)" />
                        </g>
                    </svg>
                </span>
            </button> -->
            <!--end::Toolbar-->
        </div>
        <div class="card p-1 flex-row profile-card bg-secondary mx-auto mt-7 d-none">
            <img src="<?php echo e(asset('assets/media/users/300_15.jpg')); ?>" class="rounded-circle" alt="" style="width:40px;height:40px;">
            <div class="ml-2">
                <strong><?php echo e(Auth::user()->username ?? 'Sean'); ?></strong><br>
                <span style="font-size:14px">145678978</span>
            </div>
        </div>
        </div>

        <!--end::Brand-->
        <!--begin::Aside Menu-->
        <div class="aside-menu-wrapper flex-column-fluid" id="kt_aside_menu_wrapper">
            <!--begin::Menu Container-->
            <div id="kt_aside_menu" class="aside-menu" data-menu-vertical="1" data-menu-scroll="1" data-menu-dropdown-timeout="500">
                <!--begin::Menu Nav-->
                <!-- message: masih belum bisa scroll -->
                <ul class="menu-nav overflow-hidden font-weight-bolder">
                    
                    <?php $__currentLoopData = getMenu(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                    $_active = ($loop->first) ? 'menu-item-active' : '';
                    $url = Str::startsWith($item['url'],'/')?$item['url']:"/".$item['url'];
                    ?>
                    <?php if(count($item['menu_childs']) == 0): ?>
                    <li class="menu-item <?php echo e($_active); ?> <?php echo e(Request::is($item['url'].'*') ? 'menu-item-open' : ''); ?>" aria-haspopup="true">
                        <a href="<?php echo e($url); ?>" class="menu-link">
                            <!-- <span class="menu-icon ">
                                <i class="<?php echo e($item['icon']); ?>" style="color:#fff !important;"></i>
                            </span> -->
                            <span class="menu-text"><?php echo e($item['name']); ?></span>
                        </a>
                    </li>
                    <?php else: ?>
                    <?php echo $__env->make('layouts.menucomponent',[
                    'menus'=> $item['menu_childs'],
                    'parent'=> $item['name'],
                    'parent_icon' => $item['icon'],
                    'parent_id' => $item['parent_id']
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    
                        <li class="menu-item" aria-haspopup="true">
                            <a href="<?php echo e(url('logout')); ?>" class="menu-link">
                                <span class="menu-icon ">
                                    <i class="fa fa-sign-out "></i>
                                </span>
                                <span class="menu-text text-black"><?php echo e(trans('header.logout')); ?></span>
                            </a>
                        </li>
                    
                </ul>
                <!--end::Menu Nav-->
            </div>
            <!--end::Menu Container-->
        </div>
        <!--end::Aside Menu-->
    </div>
</div>
<?php /**PATH C:\laragon\www\dev-dmm\resources\views/layouts/sidebar.blade.php ENDPATH**/ ?>