<li class="menu-item menu-item-submenu" aria-haspopup="true" data-menu-toggle="hover">
    <a href="javascript:;" class="menu-link menu-toggle">
        <?php if($parent_id == 0): ?>
            <span class="menu-icon">
                <i class="<?php echo e($parent_icon); ?>"></i>
            </span>
        <?php endif; ?>
        <span class="menu-text text-black"> <?php echo e($parent); ?> </span>
        <i class="menu-arrow text-black"></i>
    </a>
    <div class="menu-submenu">
        <i class="menu-arrow text-black"></i>
        <ul class="menu-subnav">
            <?php $__currentLoopData = $menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(count($item['menu_childs'])!=0): ?>
                    <?php echo $__env->make('layouts.menucomponent',['menus' => $item['menu_childs'],'parent'=>$item['name'],'parent_icon' => $item['icon'],'parent_id' => $item['parent_id']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>                <?php else: ?>
                <?php
                $url = Str::startsWith($item['url'],'/')?$item['url']:"/".$item['url'];
                ?>
                    <li class="menu-item <?php echo e(Request::is($item['url'].'*') ? 'menu-item-active' : ''); ?>" aria-haspopup="true">
                        <a href="<?php echo e($url); ?>" class="menu-link">
							<!-- <span class="menu-icon">
								<i class="<?php echo e($item['icon']); ?>"></i>
							</span> -->
                            <span class="menu-text menu-text"> <?php echo e($item['name']); ?> </span>
                            
                            
                            
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        </ul>
    </div>
</li>
<?php /**PATH C:\laragon\www\dev-dmm\resources\views/layouts/menucomponent.blade.php ENDPATH**/ ?>