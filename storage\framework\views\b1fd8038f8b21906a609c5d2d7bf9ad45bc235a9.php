<!DOCTYPE html>
<html lang="<?php echo e(app()->getLocale()); ?>">
<!--begin::Head-->
<head>
    
    <meta charset="utf-8"/>
    <title> <?php echo e(config('app.name')); ?> | Login </title>
    <meta name="description" content="Signin page"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--begin::Fonts-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700"/>
    <!--end::Fonts-->
    <!--begin::Page Custom Styles(used by this page)-->
    <link href="<?php echo e(asset('assets/css/pages/login/login-5.css')); ?>" rel="stylesheet" type="text/css"/>
    <!--end::Page Custom Styles-->
    <!--begin::Global Theme Styles(used by all pages)-->
    <link href="<?php echo e(asset('assets/plugins/global/plugins.bundle.css')); ?>" rel="stylesheet" type="text/css"/>
    <link href="<?php echo e(asset('assets/plugins/custom/prismjs/prismjs.bundle.css')); ?>" rel="stylesheet" type="text/css"/>
    <link href="<?php echo e(asset('assets/css/style.bundle.css')); ?>" rel="stylesheet" type="text/css"/>
    <!--end::Global Theme Styles-->
    <!--begin::Layout Themes(used by all pages)-->
    <link href="<?php echo e(asset('assets/css/themes/layout/header/base/light.css')); ?>" rel="stylesheet" type="text/css"/>
    <link href="<?php echo e(asset('assets/css/themes/layout/header/menu/light.css')); ?>" rel="stylesheet" type="text/css"/>
    <link href="<?php echo e(asset('assets/css/themes/layout/brand/dark.css')); ?>" rel="stylesheet" type="text/css"/>
    <link href="<?php echo e(asset('assets/css/themes/layout/aside/dark.css')); ?>" rel="stylesheet" type="text/css"/>
    <!--end::Layout Themes-->
    <?php echo htmlScriptTagJsApi(); ?>

    <style>
        @media  screen and (max-width:767px) {
            body{
                background-color:#ffffff !important;
            }
            .header-logo{
                display:block !important;background-image:url(<?php echo e(url('assets/img/logo/top_bg.jpg')); ?>);background-position: center;background-repeat: no-repeat;background-size:cover;
            }
        }
        @media  screen and (min-width:768px) {
            body{
                background-color:#7e818d !important;
            }
            .bfront{
                margin:auto;
            }
        }
        #pw{
            border-radius : 0.85rem 0 0 0.85rem !important;
        }
        #spw{
            border-radius : 0 0.85rem 0.85rem 0 !important;
        }
        .text-danger {
            color : red !important;
        }
        .card-img-left {
            width: 55%;
            /* Link to your background image using in the property below! */
            background: scroll center url('assets/media/bg/login_Image_Grey.png');
            background-size: cover;
        }
    </style>
</head>
<!--end::Head-->
<!--begin::Body-->
<body style="display:none;" id="kt_body"
      class="header-fixed header-mobile-fixed subheader-enabled subheader-fixed aside-enabled aside-fixed aside-minimize-hoverable page-loading">
<!--begin::Main-->
<?php if(session()->has('message_success')): ?>
    <div class="alert alert-success alert-block">
        <button type="button" class="close" data-dismiss="alert">×</button>
        <strong><?php echo e(session()->get('message_success')); ?></strong>
      </div>
<?php endif; ?>
<?php if(session()->has('message_error')): ?>
    <div class="alert alert-danger alert-block">
        <button type="button" class="close" data-dismiss="alert">×</button>
        <strong><?php echo e(session()->get('message_error')); ?></strong>
      </div>
<?php endif; ?>
<!-- login page update -->
<div class="container pt-5">
    <div class="row">
      <div class="col-lg-10 col-xl-9 mx-auto">
        <div class="card flex-row my-5 border-0 shadow rounded-3 overflow-hidden rounded-0">
            <div class="d-none d-md-flex" style="width: 55%;background: scroll center url(<?php echo e(url('assets/media/bg/login_Image_Grey.png')); ?>); background-size: cover;"></div>
            <form class="form" id="kt_login_singin_form" action="<?php echo e(url('login/store')); ?>" novalidate method="POST">
                <?php echo csrf_field(); ?>
                <div class="card-body text-center p-4 p-sm-5" style="background-color:#A44146;padding-left: 42px !important;padding-right: 42px !important;">
                    <img class="card-img-top" src="<?php echo e(asset('assets/media/logos/SIG LOGO OPREX.png')); ?>" style="align-self:center; width: 180px; height:inherit;padding: 10px;" alt=""/>
                    <h2 class="fw-bold text-white font-weight-bolder" style="font-size: 30px !important;">Operation Excellent</h2>
                    <h2 class="mb-4 fw-bold text-white font-weight-bolder" style="font-size: 25px !important;">Welcome!</h2>
                    <h6 class="card-subtitle mb-2 text-muted">Please login using your registered Username and Password</h6>
                    <div class="d-flex justify-content-between">
                        <label class="font-size-h6 font-weight-bolder text-white">Username</label>
                    </div>
                    <input class="form-control form-control-solid rounded-lg border-0"
                        type="text" name="username"
                        autocomplete="off" value="" placeholder="Enter your username" autofocus/>
                    <?php if($errors->has('username')): ?>
                        <p class="text-white"
                        style="margin-bottom:0px !important;text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('username')); ?></p>
                    <?php endif; ?>
                    <div class="d-flex justify-content-between mt-n5">
                        <label class="font-size-h6 font-weight-bolder text-white pt-5">Password</label>
                    </div>
                    <div class="input-group ">
                        <input class="form-control form-control-solid rounded-lg border-0"
                        type="password"
                        name="password"  id="pw" autocomplete="off" value="" placeholder="Enter your password"/>
                        <span class="input-group-text" id="spw"  ><a href="javascript:void(0)" id="showPw" onclick="showPW()"><i  class="fa fa-eye-slash" aria-hidden="true"></i></a></span>
                    </div>
                    <?php if($errors->has('password')): ?>
                        <p class="text-white"
                        style="margin-bottom:0px !important;text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('password')); ?></p>
                    <?php endif; ?>

                    <?php if($errors->has('sso-server')): ?>
                        <br/>
                        <p class="text-white"
                        style="margin-bottom:0px !important;text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('sso-server')); ?></p>
                    <?php endif; ?>

                    <?php if($errors->has('not-found')): ?>
                        <br/>
                        <p class="text-white"
                        style="margin-bottom:0px !important;text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('not-found')); ?></p>
                    <?php endif; ?>

                    <?php if($errors->has('message')): ?>
                        <br/>
                        <p class="text-white"
                        style="margin-bottom:0px !important;text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('message')); ?></p>
                    <?php endif; ?>

                    <div class="d-flex justify-content-between" style=";margin-bottom: -7px !important;">
                        <div class="d-flex">
                            <input class="bg-danger mr-2" type="checkbox" name="remember">
                            <label class="text-white" style="padding-top: 6px;">Remember me</label>
                        </div>
                        <!-- <a href="#" class="text-white">Forgot Password?</a> -->
                    </div>
                        <!--begin::Action-->
                    <div class="pb-lg-0 pb-5">
                        <button type="submit"
                                id="kt_login_singin_form_submit_button"
                                class="btn btn-danger btn-lg btn-block font-weight-bolder font-size-h6 px-8 py-4 my-3 mr-3">
                            Sign In
                        </button>
                    </div>
                </div>
            </form>
        </div>
      </div>
    </div>
</div>

<!-- login old display none -->

<div class="container bfront d-none" style="min-height:400px;">
    <div class="row" style="height:inherit;">
    <div class="col-4 offset-2 d-none d-sm-block" style="height:inherit;background-image:url(<?php echo e(url('assets/media/bg/login_Image_Grey.png')); ?>);background-position-x: left;background-position-y: top;background-repeat: no-repeat;background-size:contain;background-color:#A44146  ">

    </div>
    <div class="col-4"  style="background-color:#A44146">
        <div class="header-logo ml-n5 mr-n5 d-none" style="min-height:80px;"></div>
            <!--begin::Signin-->
        <div class="col-12 login-form" style="max-width: 400px;
            margin: auto;
            line-height: 1.2;">
                <!--begin::Form-->
                <form class="form" id="kt_login_singin_form" action="<?php echo e(url('login/store')); ?>" novalidate
                      method="POST">
                <?php echo csrf_field(); ?>
                <!--begin::Title-->
                    <div class="card border-0" style="background-color:#A44146">
                        <img class="card-img-top" src="<?php echo e(asset('assets/media/logos/SIG LOGO OPREX.png')); ?>" style="align-self:center; width: 180px; height:inherit;padding: 10px;" alt=""/>

                        <div class="card-body text-center">
                            <h2 class="fw-bold text-white font-weight-bolder" style="font-size: 30px !important;">Operation Excellent</h2>
                            <h2 class="mb-4 fw-bold text-white font-weight-bolder" style="font-size: 25px !important;">Welcome!</h2>
                            <h6 class="card-subtitle mb-2 text-muted">Please login using your registered username and password</h6>
                            <div class="d-flex justify-content-between">
                                <label class="font-size-h6 font-weight-bolder text-white">Username</label>
                            </div>
                            <input class="form-control form-control-solid rounded-lg border-0"
                                type="text" name="username"
                                autocomplete="off" value="" placeholder="Enter your username" autofocus/>
                            <?php if($errors->has('username')): ?>
                                <p class="text-danger"
                                style="text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('username')); ?></p>
                            <?php endif; ?>
                            <div class="d-flex justify-content-between mt-n5">
                                <label class="font-size-h6 font-weight-bolder text-white pt-5">Password</label>
                            </div>
                            <div class="input-group ">
                                <input class="form-control form-control-solid rounded-lg border-0"
                                type="password"
                                name="password"  id="pw" autocomplete="off" value="" placeholder="Enter your password"/>
                                <span class="input-group-text" id="spw"  ><a href="javascript:void(0)" id="showPw" onclick="showPW()"><i  class="fa fa-eye-slash" aria-hidden="true"></i></a></span>
                            </div>
                            <?php if($errors->has('password')): ?>
                                <p class="text-danger"
                                style="text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('password')); ?></p>
                            <?php endif; ?>

                            <?php if($errors->has('sso-server')): ?>
                                <br/>
                                <p class="text-danger"
                                style="text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('sso-server')); ?></p>
                            <?php endif; ?>

                            <?php if($errors->has('not-found')): ?>
                                <br/>
                                <p class="text-danger"
                                style="text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('not-found')); ?></p>
                            <?php endif; ?>

                            <?php if($errors->has('message')): ?>
                                <br/>
                                <p class="text-danger"
                                style="text-align:left;font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('message')); ?></p>
                            <?php endif; ?>

                            <div class="d-flex justify-content-between my-4 mt-5">
                                <div class="d-flex">
                                    <input class="bg-danger mr-2" type="checkbox" name="remember"><label class="text-white">Remember me</label>
                                </div>
                                <a href="#" class="text-white">Forgot Password?</a>
                            </div>
                             <!--begin::Action-->
                            <div class="pb-lg-0 pb-5">
                                <button type="submit"
                                        id="kt_login_singin_form_submit_button"
                                        class="btn btn-danger btn-lg btn-block font-weight-bolder font-size-h6 px-8 py-4 my-3 mr-3">
                                    Sign In
                                </button>
                            </div>
                        </div>
                    </div>
                    <!--begin::Title-->
                    <!--begin::Form group-->
                    <!-- <div class="form-group">
                        <label class="font-size-h6 font-weight-bolder text-dark">Your Username</label>
                        <input class="form-control form-control-solid py-7 px-6 rounded-lg border-0"
                               type="text" name="username"
                               autocomplete="off" value=""/>
                        <?php if($errors->has('username')): ?>
                            <p class="text-danger"
                               style="font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('username')); ?></p>
                        <?php endif; ?>

                    </div> -->
                    <!--end::Form group-->
                    <!--begin::Form group-->
                    <!-- <div class="form-group">
                        <div class="d-flex justify-content-between mt-n5">
                            <label class="font-size-h6 font-weight-bolder text-dark pt-5">Your Password</label>
                        </div>
                        <input class="form-control form-control-solid py-7 px-6 rounded-lg border-0"
                               type="password"
                               name="password" autocomplete="off" value=""/>
                        <?php if($errors->has('password')): ?>
                            <p class="text-danger"
                               style="font-size: 11px; padding-top: 0px;"><?php echo e($errors->first('password')); ?></p>
                        <?php endif; ?>
                    </div> -->
                    <!--end::Form group-->

                    <!-- <div class="form-group row">
                          <div class="col-md-6 offset-md-4">
                              <div class="checkbox">
                                  <label>
                                      <a href="<?php echo e(url('forgetpassword')); ?>">Reset Password</a>
                                  </label>
                              </div>
                          </div>
                      </div> -->
                    <!--end::Action-->
                </form>
                <!--end::Form-->
            <!--end::Signin-->
        </div>
    </div>
</div>


<!--end::Main-->

<!--begin::Global Config(global config for global JS scripts)-->
<script>var KTAppSettings = {
        "breakpoints": {"sm": 576, "md": 768, "lg": 992, "xl": 1200, "xxl": 1200},
        "colors": {
            "theme": {
                "base": {
                    "white": "#ffffff",
                    "primary": "#3699FF",
                    "secondary": "#E5EAEE",
                    "success": "#1BC5BD",
                    "info": "#8950FC",
                    "warning": "#FFA800",
                    "danger": "#F64E60",
                    "light": "#F3F6F9",
                    "dark": "#212121"
                },
                "light": {
                    "white": "#ffffff",
                    "primary": "#E1F0FF",
                    "secondary": "#ECF0F3",
                    "success": "#C9F7F5",
                    "info": "#EEE5FF",
                    "warning": "#FFF4DE",
                    "danger": "#FFE2E5",
                    "light": "#F3F6F9",
                    "dark": "#D6D6E0"
                },
                "inverse": {
                    "white": "#ffffff",
                    "primary": "#ffffff",
                    "secondary": "#212121",
                    "success": "#ffffff",
                    "info": "#ffffff",
                    "warning": "#ffffff",
                    "danger": "#ffffff",
                    "light": "#464E5F",
                    "dark": "#ffffff"
                }
            },
            "gray": {
                "gray-100": "#F3F6F9",
                "gray-200": "#ECF0F3",
                "gray-300": "#E5EAEE",
                "gray-400": "#D6D6E0",
                "gray-500": "#B5B5C3",
                "gray-600": "#80808F",
                "gray-700": "#464E5F",
                "gray-800": "#1B283F",
                "gray-900": "#212121"
            }
        },
        "font-family": "Poppins"
    };

</script>
<!--end::Global Config-->
<!--begin::Global Theme Bundle(used by all pages)-->
<script src="<?php echo e(asset('assets/plugins/global/plugins.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('assets/plugins/custom/prismjs/prismjs.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/scripts.bundle.js')); ?>"></script>
<script>
    $(document).ready(function(){
        $("#kt_body").fadeIn(3000);
    });
    function showPW() {
        var x = document.getElementById("pw");
        if (x.type === "password") {
            $('#showPw i').addClass( "fa-eye" );
            $('#showPw i').removeClass( "fa-eye-slash" );
            x.type = "text";
        } else {
            $('#showPw i').addClass( "fa-eye-slash" );
            $('#showPw i').removeClass( "fa-eye" );
            x.type = "password";
        }
    }
</script>
<!--end::Global Theme Bundle-->
<!--begin::Page Scripts(used by this page)-->

<!--end::Page Scripts-->
</body>
<!--end::Body-->
</html>
<?php /**PATH C:\laragon\www\dev-dmm\resources\views/login.blade.php ENDPATH**/ ?>