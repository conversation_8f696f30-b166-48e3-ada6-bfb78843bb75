<?php $__env->startSection('title', 'Plant Performance Realtime'); ?>

<?php $__env->startSection('css_page'); ?>
<!-- BEGIN VENDOR CSS-->
<!-- END VENDOR CSS-->

<!-- BEGIN Page Level CSS-->
<style>
        /* width */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* Track */
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        /* Handle */
        ::-webkit-scrollbar-thumb {
            background: #c8c8c8;
        }

        /* Handle on hover */
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .text-green {
            color: #335c67 !important;
        }

        .text-red {
            color: #9e2a2b !important;
        }

        .fa-sort-up {
            /* position: absolute;
            top: 50%; */
        }

        .fa-sort-down {
            /* position: absolute;
            top: 40%; */
        }

        .header-fixed .wrapper {
            padding-top: 65px !important;
        }

        .drp-calendar.right {
            display: none !important;
        }

        #labelOverlay {
            width: 90px;
            height: 45px;
            position: absolute;
            top: 53%;
            left: 42%;
            text-align: center;
            cursor: default;
        }

        #labelOverlay p {
            line-height: 0.3;
            padding: 0;
            margin: 8px;
        }

        #labelOverlay p.used-size {
            line-height: 0.5;
            font-size: 20pt;
            color: #8e8e8e;
        }

        #labelOverlay p.total-size {
            line-height: 0.5;
            font-size: 12pt;
            color: #cdcdcd;
        }

        .highcharts-tooltip > span {
            background: rgba(255, 255, 255, 0.85);
            border: 1px solid silver;
            border-radius: 3px;
            box-shadow: 1px 1px 2px #888;
            padding: 8px;
            z-index: 9999 !important;
        }

        .card-header{
            padding : 0 1rem !important;
        }

        .card.card-custom > .card-header .card-title{
            font-size: 0.9rem !important;
        }
        .card-title .font-size-sm {
            font-size: 0.825rem;
        }
        h3{
            font-size:1.275rem !important;
        }
        .table.table-separate th:last-child, .table.table-separate td:last-child {
            padding-right: 1.25rem !important;
        }
        .top-card{
            height: unset !important;
            min-height: 80px;
        }
        .font-size-top-card{
            font-family:"Calibri" !important;
            font-size: 0.73rem !important;
        }
        .font-size-title-card{
            font-family:"Calibri" !important;
            font-size: 0.73rem !important;
            /* font-size: 1.1rem !important; */
        }
        .height-pie-chart{
            height:200px !important;
            border-radius: 8px;
        }
        .height-pie-chart-opdt{
            height:210px !important;
            border-radius: 8px;
        }
        .card-header {
            padding: 0 0.3rem !important;
        }
        .padding-card{
            /* padding-right: 7px !important;
            padding-left: 7px !important; */
        }
        .padding-0-pie{
            padding:0px !important;
        }
        .padding-0-pie h3{
            padding:0px 12.5px;
        }
        .bg-header, .footer{
            margin-right: 7px !important;
            margin-left: 7px !important;
        }
        .content{
            padding: 7px 0 0 0 !important;
        }
        /* .table th{
            padding-top:0.75rem !important;
            padding-bottom:0.75rem !important;
        } */
        .calTime{
            right: 5px;
            position: absolute;
            z-index: 1;
            font-size: 9px;
            font-weight: 500;
            text-align: right;
        }
        .highcharts-credits{
            display:none;
        }
        .font-size-num-card{
            font-family:"Calibri" !important;
            font-size: 3rem !important;
        }
        .index-1{
            position: relative;
            z-index: 1;
        }
        .index-2{
            position: relative;
            z-index: 2;
        }
        .index-3{
            position: relative;
            z-index: 3;
        }
        /* th, td {
            padding:0.75rem !important;
        } */
        .card-percent{
            background: white;
            /* border-radius: 5px; */
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            /* margin-inline: 7px; */
            position:relative;
            padding-top: 8px !important;
            padding-bottom: 8px !important;
        }
        .dataTables_info{
            font-size: 10px !important;
        }
        .dataTables_wrapper .dataTable th, .dataTables_wrapper .dataTable td {
            font-size: 10px;
            padding:6px;
        }
        .dataTables_wrapper .dataTables_paginate .pagination .page-item > .page-link {
            font-size: 10px;
            height :2rem;
            min-width :2rem;
        }
        .dot {
            height: 7.5px;
            width: 7.5px;
            background-color: #EF1C1C;
            border-radius: 50%;
            display: inline-block;
        }
        .bordered{
            border:1px solid #dddddd;
            border-radius: 8px;
        }

        @media  screen and (max-width:415px) {
            .card-percent{
                /* margin-right: -7px !important; */
                /* max-width: 48%; */
            }
            .card-percent > :nth-child(even){
                /* margin-left:10px; */
            }
        }
        .margin-right-card{
            margin-right: -9.5px !important;
        }
        .upCardActiveKiln{
            background: #335C67 !important;
            border-radius: 8px;
        }
        .downCardActiveKiln{
            background: #931F26 !important;
            border-radius: 8px;
        }
        .upCard{
            background: #EBEFF0 !important;
            border-radius: 8px;
        }

        .downCard{
            background: #F4E9E9 !important;
            border-radius: 8px;
        }
        .text-green {
            color: #2AC371 !important;
        }

        .text-red {
            color: #ED2738 !important;
        }

        .top-tex-blue{
            color: #335C67 !important;
        }
        .top-tex-white{
            color: #FFFFFF !important;
        }
        .top-tex-red{
            color: #9E2A2B !important;
        }
        .upBorder{
            border: 1px solid #335C67;
            border-radius: 8px;
        }
        .downBorder{
            border: 1px solid #931F26;
            border-radius: 8px;
        }

        .dataTables_wrapper .dataTables_paginate .pagination .page-item.active > .page-link {
            background-color: #77bdd3;
        }
        .dataTables_wrapper .dataTables_paginate .pagination .page-item:hover:not(.disabled) > .page-link {
            background-color:#438794;
        }
        .badge-oph {
            color: #FFFFFF;
            background-color: #0099cc;
        }
        .badge-updt {
            color: #FFFFFF;
            background-color: #5A89A4;
        }

        .badge-pdt {
            color: #FFFFFF;
            background-color: #B4C7DA;
        }
        .badge-idle {
            color: #FFFFFF;
            background-color: #66C8DE;
        }
    </style>
<!-- END Page Level CSS-->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="content d-flex flex-column flex-column-fluid" id="kt_content" style="width: 100%!important; padding-left: 6px !important;padding-right  : 6px !important;">
    <!--begin::Subheader-->
    <div class="container-fluid content-mobile">
        <!-- <div class="card row mb-1 filter-mobile">
            <div class="col-12 col-md style-top-card">
                <div class="d-flex justify-content-end align-items-center flex-no-wrap h-100">
                    <div id="loader_opco" class="flex-grow-1 filter-drop-down">
                        <select id="filter_data_opco" name="filter_data_opco" class="select2 wd-100 form-control form-control-lg" style="width:100%;">
                            <option value="" selected>All Opco</option>
                        </select>
                    </div>
                    <div id="loader_plant" class="flex-grow-1 filter-drop-down ml-1">
                        <select id="filter_data_plant" name="filter_data_plant" class="select2 wd-100 form-control form-control-lg" style="width:100%;">
                            <option value="" selected>All Plant</option>
                        </select>
                    </div>
                    <button type="button" onclick="changeFilterData();" class="btn btn-filter-apply btn-sm ml-1">
                        Apply
                    </button>
                    <a href="/perform-history" type="button" class="btn btn-filter-history btn-sm ml-1">History</a>
                </div>
            </div>
        </div> -->
        <!--begin::Notice-->
        <!--end::Notice-->
        <!--begin::Card-->

        <!--end::Card-->
        <div class="row mb-1">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-3 col-md padding-card style-top-card" id="loader_netto_availability">
                        <div class="card-percent" id="card_netto_availability">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder text-dark font-size-top-card">Klin Netto Availability</span>
                                <span id="value_card_availability"></span>
                                <span id="target_card_availability" class="text-muted font-weight-bold font-size-sm"></span>
                            </h3>
                        </div>
                    </div>
                    <div class="col-3 col-md padding-card style-top-card" id="loader_netto_yield">
                        <div class="card-percent" id="card_netto_yield">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder text-dark font-size-top-card">Kiln Netto Yield</span>
                                <span id="value_card_yield"></span>
                                <span id="target_card_yield" class="text-muted font-weight-bold font-size-sm"></span>
                            </h3>
                        </div>
                    </div>
                    <div class="col-3 col-md padding-card style-top-card" id="loader_netto_oee">
                        <div class="card-percent" id="card_netto_oee">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder text-dark font-size-top-card">Kiln Netto OEE</span>
                                <span id="value_card_oee"></span>
                                <span id="target_card_oee" class="text-muted font-weight-bold font-size-sm"></span>
                            </h3>
                        </div>
                    </div>
                    <div class="col-3 col-md padding-card style-top-card" id="loader_kiln_mtbf">
                        <div class="card-percent" id="card_kiln_mtbf">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder text-dark font-size-top-card">Kiln MTBF (hour)</span>
                                <span id="value_card_mtbf"></span>
                                <span id="target_card_mtbf" class="text-muted font-weight-bold font-size-sm"></span>
                            </h3>
                        </div>
                    </div>
                    <div class="col-3 col-md padding-card style-top-card" id="loader_kiln_mttr">
                        <div class="card-percent" id="card_kiln_mttr">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder text-dark font-size-top-card">Kiln MTTR (hour)</span>
                                <span id="value_card_mttr"></span>
                                <span id="target_card_mttr" class="text-muted font-weight-bold font-size-sm"></span>
                            </h3>
                        </div>
                    </div>
                    <div class="col-3 col-md padding-card style-top-card" id="loader_kiln_ratio_updt">
                        <div class="card-percent" id="card_kiln_ratio_updt">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder text-dark font-size-top-card">Kiln Ratio Updt</span>
                                <span id="value_card_ratio"></span>
                                <span id="target_card_ratio" class="text-muted font-weight-bold font-size-sm"></span>
                            </h3>
                        </div>
                    </div>
                    <div class="col-3 col-md padding-card style-top-card" id="loader_frek_updt">
                        <div class="card-percent" id="card_atas_frek_updt">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder text-dark font-size-top-card">Frek. UPDT</span>
                                <span id="card_frek_updt"></span>
                                <span id="target_card_frek_updt" class="text-muted font-weight-bold font-size-sm"></span>
                            </h3>
                        </div>
                    </div> 
                    <div class="col-3 col-md padding-card style-top-card" id="loader_active_kiln">
                        <div class="card-percent" id="card_atas_active_kiln">
                            <h3 class="card-title flex-column m-auto d-flex text-center">
                                <span class="font-weight-bolder top-tex-white font-size-top-card">Active Kiln</span>
                                <span id="card_active_kiln"></span>
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-1">
            <div class="col-12 col-md style-top-card">
                <div class="container-fluid bordered bg-white" id="realtime_rate_overlay">
                    <div id="kt_flotcharts_1" class="height-pie-chart"></div>
                </div>
            </div>
            <div class="col-12 col-md style-top-card">
                <div class="container-fluid bordered bg-white h-100 padding-0-pie" id="loader_bottom_frek_updt">
                        <div id="kt_bargchart_1" class="height-pie-chart"></div>
                </div>
            </div>
        </div>
        <div class="row mb-1">
            <div class="col-md-12 style-top-card">
                <div class="container-fluid bordered bg-white" id = "plant_event_overlay">
                        <h3 class="text-dark font-weight-bolder mt-2 font-size-title-card">Plant Event</h3>
                        <table id="kt_datatable" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th scope="col">Date</th>
                                    <th scope="col">Plant</th>
                                    <th scope="col">Description</th>
                                    <th scope="col">Cause</th>
                                    <th scope="col">Time</th>
                                    <th scope="col">Spent (H)</th>
                                </tr>
                            </thead>
                    </table>
                </div>
            </div>
        </div>
        <div class="row mb-1">
            <div class="col-12 col-md-6">
                <div class="row margin-right-card">
                    <div class="col-12 style-top-card">
                        <div class="container-fluid padding-0-pie bordered bg-white" id="stack_op_overlay">
                            <div id="kt_bargchart_2" class="height-pie-chart-opdt"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6">
                <div class="row">
                    <div class="col-12 col-md style-top-card">
                        <div class="container-fluid padding-0-pie index-3 bordered bg-white" id="loader_real_summary">
                            <div id="container" class="height-pie-chart-opdt"></div>
                        </div>
                    </div>
                    <div class="col-12 col-md style-top-card">
                        <div class="container-fluid padding-0-pie index-2 bordered bg-white" id="loader_rkap_summary">
                            <div id="container_rkap_summary" class="height-pie-chart-opdt"></div>
                        </div>
                    </div>
                    <div class="col-12 col-md style-top-card">
                        <div class="container-fluid padding-0-pie index-1 bordered bg-white" id="loader_rate_summary">
                            <div id="container_rate_summary" class="height-pie-chart-opdt"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-1">
            <div class="col-12 col-md style-top-card">
                <div class="container-fluid bordered bg-white padding-0-pie" id="loader_kiln_opdt2">
                    <div id="kt_bargchart_3" class="height-pie-chart"></div>
                </div>
            </div>
            <div class="col-12 col-md style-top-card">
                <div class="container-fluid bordered bg-white padding-0-pie" id="kiln_rate_overlay">
                    <div id="kiln_rate_chart" class="height-pie-chart"></div>
                </div>
            </div>
        </div>
        <div class="row mb-1">
            <div class="col-12 col-md style-top-card">
                <div class="container-fluid bordered bg-white padding-0-pie" id="kiln_gros_oee_overlay">
                    <div id="kiln_gros_oee" class="height-pie-chart"></div>
                </div>
            </div>
            <div class="col-12 col-md style-top-card">
                <div class="container-fluid bordered bg-white padding-0-pie" id="loader_kiln_oee">
                    <div id="kiln_netto_oee" class="height-pie-chart"></div>
                </div>
            </div>
        </div>
    </div>
    <!--end::Subheader-->

</div>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_page'); ?>
<!--begin::Page Vendors(used by this page)-->
<!--end::Page Vendors-->
<!--begin::Page Scripts(used by this page)-->
<!--end::Page Scripts-->
<script src="//www.google.com/jsapi"></script>

<script src="https://code.highcharts.com/stock/highstock.js"></script>
<!-- <script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://code.highcharts.com/modules/export-data.js"></script> -->
<script src="https://code.highcharts.com/modules/accessibility.js"></script>
<script src="https://code.highcharts.com/modules/data.js"></script>
<script type="text/javascript">
    var qFilter = "All OpcoAll Plant";
    let chart;
    // var table = $('#kt_datatable');
    $(document).ready(function() {
        // $("body#kt_body").addClass('aside-minimize');
        getFilterPerfom();
        $('.select2').select2();
        // allCard();
    });

    $('#filter_data_opco').on('change', function() {
        let opco = $(this).val()
        getPlantByOpco(opco)
    });

    function changeFilterData() {
        // getFilterPerfom();
        // $('.select2').select2();
        allCard();
    }

    function getPlantByOpco(opco) {
        $.ajax({
            type: "POST",
            url: "/get-plant-by-opco",
            data: {
                opco: opco,
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_plant").LoadingOverlay("show");
            },
            success: function(data) {
                $("#loader_plant").LoadingOverlay("hide");
                $('#filter_data_plant').attr('disabled', false);
                $('#filter_data_plant').empty()
                $('#filter_data_plant').append('<option value="" selected>All Plant</option>')
                $.each(data.plant, function(i, item) {
                    var newOption = new Option(item.kode_plant, item.kode_plant, false, false);
                    $('#filter_data_plant').append(newOption);
                });
                if (data.noData) {
                    $('#filter_data_plant').attr('disabled', true);
                }
            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_plant").LoadingOverlay("hide");
        });
    }

    function getFilterPerfom() {
        $.ajax({
            type: "POST",
            url: "/get-filter-perform",
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_opco").LoadingOverlay("show");
                $("#loader_plant").LoadingOverlay("show");
            },
            success: function(data) {
                $.each(data.opco, function(i, item) {
                    var newOption = new Option(item.nama_opco, item.kode_opco, false, false);
                    $('#filter_data_opco').append(newOption);
                });
                $.each(data.tahun, function(i, item) {
                    var newOption = new Option(item.tahun, item.tahun, false, false);
                    $('#filter_data_tahun').append(newOption);
                });
                allCard();
            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_opco").LoadingOverlay("hide");
            $("#loader_plant").LoadingOverlay("hide");
        });
    }

    function allCard() {
        let fontSize = 7
        let colorOPH = '#0099CC'
        let colorPDT = '#B4C7DA'
        let colorUPDT = '#5A89A4'
        let colorIDLE = '#66C8DE'
        let colorFYSTOP = '#A9A9A9'
        let colorRKAP = '#CA4A1C'
        let colorReal = '#B4C7DA'
        
        getNettoAvailability();
        cardNettoYield();
        cardNettoOEE();
        cardMTBF();
        cardMTTR();
        cardRatioUpdt();
        cardFrekUpdt();
        cardActiveKiln();
        var plant = $("#filter_data_plant").val();
        if (plant != '') {
            enablePoolingRT = true;
            clearInterval(idIntKilnRate);
            chartRealtime();
        } else {
            enablePoolingRT = false;
            kilnRateRealtime(function(datax){
                realtimefirst(datax);
            });
            // kilnRateRealtime();
        }
        dataFrekUPDT();
        getPlantEvent();

        datastackOpDetail(colorOPH, colorPDT, colorUPDT, colorIDLE, colorFYSTOP, colorRKAP, fontSize);
        realSummary();
        rkapSummary();
        rateSummary();
        chartKilnNAI(colorReal, colorRKAP, fontSize);
        chartKilnRATE(colorOPH, colorReal, colorRKAP, fontSize);
        chartKilnGrosOEE(colorReal, colorRKAP, fontSize);
        chartKilnNettoOEE();
    }

    function getNettoAvailability() {
        $.ajax({
            type: "POST",
            url: "./get-availability-performance-real",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_netto_availability").LoadingOverlay("show");
            },
            success: function(data) {
                var split = data.data.split("#");

                if (split[2] >= 0) {
                        $("#value_card_availability").attr('class','font-weight-bolder font-size-h1 top-tex-blue');
                        $("#card_netto_availability").attr('class','card-percent upCard');
                        $("#loader_netto_availability").attr('class','col-3 col-md padding-card style-top-card upBorder');
                        var selisih = '+';
                        var icon = '<span class="fa fa-sort-up"></span>';
                    } else {
                        $("#value_card_availability").attr('class','font-weight-bolder font-size-h1 top-tex-red');
                        $("#card_netto_availability").attr('class','card-percent downCard');
                        $("#loader_netto_availability").attr('class','col-3 col-md padding-card style-top-card downBorder');
                        var selisih = '';
                        var icon = '<span class="fa fa-sort-down"></span>';
                    }

                $("#value_card_availability").html(split[0] + '% ' + icon);
                $("#target_card_availability").text('Goal: ' + split[1] + '% (' + selisih + split[2] + '%)');
                setTimeout(getNettoAvailability, 600000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_netto_availability").LoadingOverlay("hide");
        });
    }

    function cardNettoYield() {
        $.ajax({
            type: "POST",
            url: "./get-card-netto-yield",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_netto_yield").LoadingOverlay("show");
            },
            success: function(data) {
                var split = data.data.split("#");

                if (split[2] >= 0) {
                    $("#value_card_yield").attr('class','font-weight-bolder font-size-h1 top-tex-blue');
                    $("#card_netto_yield").attr('class','card-percent upCard');
                    $("#loader_netto_yield").attr('class','col-3 col-md padding-card style-top-card upBorder');
                    var selisih = '+';
                    var icon = '<span class="fa fa-sort-up"></span>';
                } else {
                    $("#value_card_yield").attr('class','font-weight-bolder font-size-h1 top-tex-red');
                    $("#card_netto_yield").attr('class','card-percent downCard');
                    $("#loader_netto_yield").attr('class','col-3 col-md padding-card style-top-card downBorder');
                    var selisih = '';
                    var icon = '<span class="fa fa-sort-down"></span>';
                }

                $("#value_card_yield").html(split[0] + '% ' + icon);
                $("#target_card_yield").text('Goal: ' + split[1] + '% (' + selisih + split[2] + '%)');
                setTimeout(cardNettoYield, 600000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_netto_yield").LoadingOverlay("hide");
        });
    }

    function cardNettoOEE() {
        $.ajax({
            type: "POST",
            url: "./get-card-netto-oee",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_netto_oee").LoadingOverlay("show");
            },
            success: function(data) {
                var split = data.data.split("#");

                if (split[2] >= 0) {
                    $("#value_card_oee").attr('class','font-weight-bolder font-size-h1 top-tex-blue');
                    $("#card_netto_oee").attr('class','card-percent upCard');
                    $("#loader_netto_oee").attr('class','col-3 col-md padding-card style-top-card upBorder');
                    var selisih = '+';
                    var icon = '<span class="fa fa-sort-up"></span>';
                } else {
                    $("#value_card_oee").attr('class','font-weight-bolder font-size-h1 top-tex-red');
                    $("#card_netto_oee").attr('class','card-percent downCard');
                    $("#loader_netto_oee").attr('class','col-3 col-md padding-card style-top-card downBorder');
                    var selisih = '';
                    var icon = '<span class="fa fa-sort-down"></span>';
                }

                $("#value_card_oee").html(split[0] + '% ' + icon);
                $("#target_card_oee").text('Goal: ' + split[1] + '% (' + selisih + split[2] + '%)');
                setTimeout(cardNettoOEE, 600000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_netto_oee").LoadingOverlay("hide");
        });
    }

    function cardMTBF() {
        $.ajax({
            type: "POST",
            url: "./get-card-mtbf",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_kiln_mtbf").LoadingOverlay("show");
            },
            success: function(data) {
                var split = data.data.split("#");

                if (split[2] >= 0) {
                        $("#value_card_mtbf").attr('class','font-weight-bolder font-size-h1 top-tex-blue');
                        $("#card_kiln_mtbf").attr('class','card-percent upCard');
                        $("#loader_kiln_mtbf").attr('class','col-3 col-md padding-card style-top-card upBorder');
                        var selisih = '+';
                        var icon = '<span class="fa fa-sort-up"></span>';
                    } else {
                        $("#value_card_mtbf").attr('class','font-weight-bolder font-size-h1 top-tex-red');
                        $("#card_kiln_mtbf").attr('class','card-percent downCard');
                        $("#loader_kiln_mtbf").attr('class','col-3 col-md padding-card style-top-card downBorder');
                        var selisih = '';
                        var icon = '<span class="fa fa-sort-down"></span>';
                    }

                $("#value_card_mtbf").html(split[0] + ' ' + icon);
                $("#target_card_mtbf").text('Goal: ' + split[1] + ' (' + selisih + split[2] + '%)');
                setTimeout(cardMTBF, 600000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_kiln_mtbf").LoadingOverlay("hide");
        });
    }

    function cardMTTR() {
        $.ajax({
            type: "POST",
            url: "./get-card-mttr",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_kiln_mttr").LoadingOverlay("show");
            },
            success: function(data) {
                var split = data.data.split("#");

                if (split[2] > 0) {
                    $("#value_card_mttr").attr('class','font-weight-bolder font-size-h1 top-tex-blue');
                    $("#card_kiln_mttr").attr('class','card-percent upCard');
                    $("#loader_kiln_mttr").attr('class','col-3 col-md padding-card style-top-card upBorder');
                    var selisih = '+';
                    var icon = '<span class="fa fa-sort-up"></span>';
                } else {
                    $("#value_card_mttr").attr('class','font-weight-bolder font-size-h1 top-tex-red');
                    $("#card_kiln_mttr").attr('class','card-percent downCard');
                    $("#loader_kiln_mttr").attr('class','col-3 col-md padding-card style-top-card downBorder');
                    var selisih = '';
                    var icon = '<span class="fa fa-sort-down"></span>';
                }

                $("#value_card_mttr").html(split[0] + ' ' + icon);
                $("#target_card_mttr").text('Goal: ' + split[1] + ' (' + selisih + split[2] + '%)');
                setTimeout(cardMTTR, 600000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_kiln_mttr").LoadingOverlay("hide");
        });
    }

    function cardRatioUpdt() {
        $.ajax({
            type: "POST",
            url: "./get-card-ratio-updt",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_kiln_ratio_updt").LoadingOverlay("show");
            },
            success: function(data) {
                var split = data.data.split("#");

                if (split[2] > 0) {
                    $("#value_card_ratio").attr('class','font-weight-bolder font-size-h1 top-tex-blue');
                    $("#card_kiln_ratio_updt").attr('class','card-percent upCard');
                    $("#loader_kiln_ratio_updt").attr('class','col-3 col-md padding-card style-top-card upBorder');
                    var selisih = '+';
                    var icon = '<span class="fa fa-sort-up"></span>';
                } else {
                    $("#value_card_ratio").attr('class','font-weight-bolder font-size-h1 top-tex-red');
                    $("#card_kiln_ratio_updt").attr('class','card-percent downCard');
                    $("#loader_kiln_ratio_updt").attr('class','col-3 col-md padding-card style-top-card downBorder');
                    var selisih = '';
                    var icon = '<span class="fa fa-sort-down"></span>';
                }

                $("#value_card_ratio").html(split[0] + '% ' + icon);
                $("#target_card_ratio").text('Goal: ' + split[1] + '% (' + selisih + split[2] + '%)');
                setTimeout(cardRatioUpdt, 600000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_kiln_ratio_updt").LoadingOverlay("hide");
        });
    }

    function cardFrekUpdt() {
        $.ajax({
            type: "POST",
            url: "./get-card-frek-updt",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_frek_updt").LoadingOverlay("show");
            },
            success: function(data) {
                var split = data.data.split("#");

                if (split[2] > 0) {
                    $("#card_frek_updt").attr('class','font-weight-bolder font-size-h1 top-tex-blue');
                    $("#card_atas_frek_updt").attr('class','card-percent upCard');
                    $("#loader_frek_updt").attr('class','col-3 col-md padding-card style-top-card upBorder');
                    var selisih = '+';
                    var icon = '<span class="fa fa-sort-up"></span>';
                } else {
                    $("#card_frek_updt").attr('class','font-weight-bolder font-size-h1 top-tex-red');
                    $("#card_atas_frek_updt").attr('class','card-percent downCard');
                    $("#loader_frek_updt").attr('class','col-3 col-md padding-card style-top-card downBorder');
                    var selisih = '';
                    var icon = '<span class="fa fa-sort-down"></span>';
                }
                $("#card_frek_updt").html(split[0] + ' ' + icon);
                $("#target_card_frek_updt").text('Goal: ' + split[1] + ' (' + selisih + split[2] + ')');

                setTimeout(cardFrekUpdt, 600000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_frek_updt").LoadingOverlay("hide");
        });
    }

    function cardActiveKiln() {
        $.ajax({
            type: "POST",
            url: "./get-card-active-kiln",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $('#loader_active_kiln').LoadingOverlay("show");
            },
            success: function(data) {
                if (data.data > 0) {
                    $("#card_active_kiln").attr('class', 'font-weight-bolder font-size-num-card top-tex-white');
                    $("#card_atas_active_kiln").attr('class','card-percent upCardActiveKiln');
                } else {
                    $("#card_active_kiln").attr('class', 'font-weight-bolder font-size-num-card top-tex-white');
                    $("#card_atas_active_kiln").attr('class','card-percent downCardActiveKiln');
                }
                $("#card_active_kiln").html(data.data);
                setTimeout(cardActiveKiln, 30000); //(600 s)

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $('#loader_active_kiln').LoadingOverlay("hide");
        });
    }

    function kilnRateRealtime(handleData) {
        $.ajax({
            type: "POST",
            url: "./get-kiln-rate-realtime",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#realtime_rate_overlay").LoadingOverlay("show");
            },
            success: function(data) {
                var array_kiln_rate = [];
                var array = [];

                $.each(data.data, function(i, item) {
                    array = new Array(item.kode_plant, Math.round(parseFloat(item.kiln_rate) * 100) / 100);
                    array_kiln_rate.push(array);
                });
                handleData(array_kiln_rate);
                // realtimefirst(array_kiln_rate)
                // setTimeout(kilnRateRealtime, 60000); //(60 s)
            },
            error: function(xmlhttprequest, textstatus, message) {
                return '';
            }
        }).always(function() {
            $("#realtime_rate_overlay").LoadingOverlay("hide");
        });
    }

    var idIntKilnRate = 0;
    function realtimefirst(data) {
        $("#kt_flotcharts_1").empty();

        if (data == '') {
            data = [];
        } else {
            data = data;
        }
        Highcharts.chart('kt_flotcharts_1', {
            chart: {
                type: 'column',
                style: {
                    fontFamily: 'Calibri, Helvetica, "sans-serif"',
                },
                animation: Highcharts.svg,
                events: {
                    load: function () {
                        var series = this.series[0];
                        idIntKilnRate = setInterval(function(){
                           kilnRateRealtime(function(datax){
                                    series.setData(datax);
                                });
                        }, 30000); //in 30'
                    }
                }
            },
            title: {
                text: 'Kiln Rate (TPD)',
                align: 'left',
                verticalAlign: 'top',
                style: {
                    fontWeight: 'bold',
                    fontSize: 10
                }
            },
            subtitle: {
                text: ''
            },
            xAxis: {
                type: 'category',
                labels: {
                    rotation: -90,
                    style: {
                        fontSize: 7,
                        fontFamily: "Calibri"
                    }
                }
            },
            yAxis: {
                title: false,
                endOnTick: false,
                tickAmount: 3,
                labels:{
                    style:{
                        fontSize: 7,
                        fontFamily: "Calibri"
                    }
                },
            },
            plotOptions: {
                series:{
                    pointWidth: 21,
                    pointPadding: 0.15,
                    groupPadding: 0,
                    borderWidth: 0,
                    shadow: false
                }
            },
            legend: {
                enabled: false
            },
            series: [{
                name: 'Kiln Rate (TPD)',
                color: '#77BDD3',
                data: data,
                dataLabels: {
                    enabled: true,
                    rotation: -90,
                    color: '#FFFFFF',
                    align: 'right',
                    format: '{point.y:.1f}', // one decimal
                    y: 10, // 10 pixels down from the top
                    style: {
                        textShadow: false,
                        textOutline: "0px",
                        fontSize: 8,
                        fontFamily: "Calibri"
                    }
                }
            }]
        });
    }

    var enablePoolingRT = true;
    function chartRealtime() {
        $("#kt_flotcharts_1").empty();
        $("#realtime_rate_overlay").LoadingOverlay("show");

        Highcharts.getJSON('./line-real-time-chart/'+ $("#filter_data_plant").val(), data => {
            const chart = Highcharts.stockChart('kt_flotcharts_1', {
                chart: {
                    style: {
                        fontFamily: 'Calibri, Helvetica, "sans-serif"',
                    },
                    events: {
                        load: function() {
                        const series = this.series[0];
                        setInterval(function() {
                            Highcharts.getJSON('./line-real-time-chart/'+ $("#filter_data_plant").val(), function(data) {
                                            const newPoint = data[data.length -1];

                            series.addPoint([newPoint[0], newPoint[1]], true, true);
                            });
                        }, 30000);
                        }
                    }
                },

                title: {
                    text: 'Kiln Rate (TPD)',
                    align: 'left',
                    verticalAlign: 'top',
                    margin: 0,
                    padding: 0,
                    style: {
                        fontWeight: 'bold',
                        fontSize: 10
                    }
                },

                subtitle: {
                    text: ''
                },

                rangeSelector: {
                    buttons: [{
                        count: 1,
                        type: 'hour',
                        text: '1H'
                    }, {
                        count: 1,
                        type: 'day',
                        text: '1D'
                    }, {
                        count: 1,
                        type: 'month',
                        text: '1M'
                    }, {
                        type: 'all',
                        text: 'All'
                    }],
                    inputEnabled: false,
                    selected: 0,
                },
                navigator: {
                    height: 10
                },
                series: [{
                    name: 'Kiln Rate',
                    data: data,
                    type: 'areaspline',
                    threshold: null,
                    tooltip: {
                        valueDecimals: 2
                    },
                    fillColor: {
                linearGradient: {
                    x1: 0,
                    y1: 0,
                    x2: 0,
                    y2: 1
                },
                stops: [
                    [0, Highcharts.getOptions().colors[0]],
                    [1, Highcharts.color(Highcharts.getOptions().colors[0]).setOpacity(0).get('rgba')]
                ]
            },
                }],

                responsive: {
                    rules: [{
                        condition: {
                            maxWidth: 500
                        },
                        // chartOptions: {
                        //     chart: {
                        //         height: 300
                        //     },
                        //     subtitle: {
                        //         text: null
                        //     },
                        //     navigator: {
                        //         enabled: false
                        //     }
                        // }
                    }]
                }
            });

        });

        $("#realtime_rate_overlay").LoadingOverlay("hide");
    }

    function dataFrekUPDT() {
        $.ajax({
            type: "POST",
            url: "./frek-updt-chart",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#kt_bargchart_1").empty();
                $("#loader_bottom_frek_updt").LoadingOverlay("show");
            },
            success: function(data) {
                chartFreqUPDT(data);
                setTimeout(dataFrekUPDT, 60000); //(600 s)
            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_bottom_frek_updt").LoadingOverlay("hide");
        });
    }
    function chartFreqUPDT(data) {
        Highcharts.chart('kt_bargchart_1', {
            chart: {
                type: 'column',
                style: {
                    fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                },
            },
            title: {
                text:'Frek. UPDT',
                align: 'left',
                verticalAlign: 'top',
                margin: 0,
                padding: 0,
                style: {
                    fontWeight: 'bold',
                    fontSize: 10
                }
            },
            subtitle: {
                text: ''
            },
            legend: {
                align: 'left',
                verticalAlign: 'top',
                x: 0,
                y: 0,
                itemStyle: {
                    fontWeight: 500,
                    fontSize: 10
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        allowOverlap: true,
                        enabled: true,
                    }
                },
                series:{
                    pointWidth: 21,
                    pointPadding: 0,
                    groupPadding: 0.1,
                    borderWidth: 0,
                    shadow: false
                }
            },
            yAxis: {
                title: {
                    useHTML: true,
                    text: ''
                },
                labels:{
                    style:{
                        fontSize: 7,
                        fontFamily: 'Calibri'
                    }
                },
                endOnTick: false,
                tickAmount: 3,
            },
            xAxis: {
                categories: data.kode_plant,
                labels:{
                    rotation:-90,
                    style:{
                        fontSize: 7,
                        fontFamily: 'Calibri'
                    }
                }
            },
            tooltip: {
                headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +'<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
                footerFormat: '</table>',
                shared: true,
                useHTML: true
            },
            series: [{
                name: 'Freq REAL',
                    color: '#B4C7DA',
                    type: 'column',
                    data: data.frek_real,
                },{
                    name: 'Freq RKAP',
                    color: '#CA4A1C',
                    type: 'spline',
                    data: data.frek_rkap,
                }
            ]
        });

    }

    var plantEventDatatable = $('#kt_datatable').DataTable({
            processing: true,
            serverSide: true,
            responsive: false,
            bFilter: false,
            deferLoading: 0,
            pageLength: 5,
            lengthChange: false,
            scrollX: true,
            fixedColumns: false,
            order: [[0, 'desc']],
            fnRowCallback: function(nRow, aData, iDisplayIndex, iDisplayIndexFull) {
                if (aData['deskripsi'] === '-' && aData['kategori'] == '-') {
                    $('td', nRow).addClass('bg-invalid-table');
                }
            },
            ajax: {
                "url": "./get-plant-event",
                'type': 'POST',
                'dataType': 'JSON',
                "data": function(d) {
                    return $.extend({}, d, {
                        "opco": $("#filter_data_opco").val(),
                        "plant": $("#filter_data_plant").val(),
                    });
                },
                'error': function(xhr, textStatus, ThrownException) {
                    alert('Error loading data. Exception: ' + ThrownException + "\n" + textStatus);
                }
            },
            columns: [{
                data: "tanggal_mulai",
                name: "tanggal_mulai",
                width:'80px',
                render: function (data, type, full, meta) {
                return moment(data).format('DD MMM YYYY');
            }
            }, {
                data: "kode_plant",
                name: "kode_plant",
                width:'43px',
            },{
                data: "deskripsi",
                name: "deskripsi",
            },{
                data: "kategori",
                name: "kategori",
                width:'50px',
                render: function(data, type, full, meta) {
                    var className = "";
                    if (data == '') {
                        className = 'dot';
                        data = '';
                    }else if (data == '-') {
                        className = 'badge badge-pill font-weight-bolder badge-secondary';
                    }else if (data == 'INVALID') {
                        className = 'badge badge-pill font-weight-bolder badge-oph';
                    }else if (data == 'UPDT') {
                        className = 'badge badge-pill font-weight-bolder badge-updt';
                    }
                    else if (data == 'STOP IDLE') {
                        className = 'badge badge-pill font-weight-bolder badge-idle';
                    }
                    else if (data == 'PDT') {
                        className = 'badge badge-pill font-weight-bolder badge-pdt';
                    }
                    return '<span class="' + className + '">' + data + '</span>';
                }
            },{
                data: "time",
                name: "time",
                width:'40px',
                className: "dt-center",
            }, {
                data: 'spent',
                name: 'spent',
                width:'70px',
                className: "dt-center",
            }, ]
        });

    function getPlantEvent() {
        $("#plant_event_overlay").LoadingOverlay("show");
        plantEventDatatable.draw()
        $("#plant_event_overlay").LoadingOverlay("hide");
    }
    var initTable = function() {
        table = $('#kt_datatable').DataTable({
            scrollX: true,
            scrollY: '250px',
            scrollCollapse: true,
        });
    }

    function datastackOpDetail(colorOPH, colorPDT, colorUPDT, colorIDLE, colorFYSTOP, colorRKAP, fontSize) {
        $.ajax({
            type: "POST",
            url: "./kiln-op-details",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#kt_bargchart_2").empty();
                $("#stack_op_overlay").LoadingOverlay("show");
            },
            success: function(data) {
                stackOpDetail(data,colorOPH, colorPDT, colorUPDT, colorIDLE, colorFYSTOP, colorRKAP, fontSize);
                setTimeout(dataFrekUPDT, 600000); //(600 s)
            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#stack_op_overlay").LoadingOverlay("hide");
        });
    }
    function stackOpDetail(data, colorOPH, colorPDT, colorUPDT, colorIDLE, colorFYSTOP, colorRKAP, fontSize) {
        Highcharts.chart('kt_bargchart_2', {
            chart: {
                style: {
                    fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                },
            },
            title: {
                text:'Kiln Operation Details (hour)',
                align: 'left',
                verticalAlign: 'top',
                margin: 0,
                padding: 0,
                style: {
                    fontWeight: 'bold',
                    fontSize: 10
                }
            },
            legend: {
                align: 'left',
                verticalAlign: 'top',
                x: 0,
                y: 0,
                itemStyle: {
                    fontWeight: 500,
                    fontSize: 10
                }
            },
            plotOptions: {
                column: {
                    stacking: 'normal'
                },
                line: {
                    dataLabels: {
                        allowOverlap: true,
                        enabled: false
                    }
                },
                series:{
                    pointWidth: 21,
                    pointPadding: 0.15,
                    groupPadding: 0,
                    borderWidth: 0,
                    shadow: false
                }
            },
            yAxis: {
                title: {
                    text: ''
                },
                labels:{
                    style:{
                        fontSize: 7,
                        fontFamily: 'Calibri'
                    }
                },
                endOnTick: false,
                tickAmount: 3,
            },
            xAxis: {
                categories: data.kode_plant,//['GHO1', 'GHO2', 'GHO3','GHO4','SG','SP2','SP3','SP4','SP6','ST2','ST3','ST4']
                labels:{
                    rotation:-90,
                    style:{
                        fontSize: 7,
                        fontFamily: 'Calibri'
                    }
                }
            },
            series: [{
                    name: 'FY STOP',
                    color: colorFYSTOP,
                    type: 'column',
                    data: data.real_fy_stop, //[42.4, 33.2, 34.5, 39.7, 52.6, 75.5, 57.4, 60.4, 47.6, 39.1, 46.8, 51.1]
                },
                {
                    name: 'PDT',
                    color: colorPDT,
                    type: 'column',
                    data: data.real_pdt, //[42.4, 33.2, 34.5, 39.7, 52.6, 75.5, 57.4, 60.4, 47.6, 39.1, 46.8, 51.1]
                }, {
                    name: 'UPDT',
                    color: colorUPDT,
                    type: 'column',
                    data: data.real_updt, //[48.9, 38.8, 39.3, 41.4, 47.0, 48.3, 59.0, 59.6, 52.4, 65.2, 59.3, 51.2]
                }, {
                    name: 'Stop IDLE',
                    color: colorIDLE,
                    type: 'column',
                    data: data.real_stop_idle, //[83.6, 78.8, 98.5, 93.4, 106.0, 84.5, 105.0, 104.3, 91.2, 83.5, 106.6, 92.3]
                }, {
                    name: 'Op. H',
                    color: colorOPH,
                    type: 'column',
                    data: data.real_oph, //[49.9, 71.5, 106.4, 129.2, 144.0, 176.0, 135.6, 148.5, 216.4, 194.1, 95.6, 54.4]
                }, {
                    name: 'RKAP Op.',
                    data: data.rkap_oph, //[200, 300, 250, 400, 400, 300, 200, 300, 400, 200, 300, 300],
                    step: 'center',
                    color: colorRKAP,
                }
            ]
        });

    }

    function realSummary() {
        $.ajax({
            type: "POST",
            url: "./real-summary-graph",
            dataType: 'JSON',
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            // timeout: 10000,
            beforeSend: function() {
                $("#loader_real_summary").LoadingOverlay("show");
            },
            success: function(data) {
                $('#calTimeReal').empty()
                var time = kFormatter(parseInt(data.calTime)).toString()
                var chart1 = new Highcharts.Chart({
                    chart: {
                        style: {
                            fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                        },
                        type: "pie",
                        renderTo: "container",
                        options:{
                            cutoutPercentage: 75,
                        },
                        events: {
                            render: function() {
                                var chart = this,
                                    rend = chart.renderer,
                                    pie = chart.series[0],
                                    left = chart.plotWidth / 2 + chart.plotLeft,
                                    top = chart.plotHeight / 2 + chart.plotTop;
                                    leftb = left+40;
                                    topb = top-80;
                                var labelText = '<span style="color: black">'+time+'<br/>' + '<span style="color: black">Cal. Time (hour)';
                                    if (!chart.bottomText) {
                                    chart.bottomText = chart.renderer.text(labelText, leftb, topb, true)
                                        .attr({
                                            zIndex: 5,
                                            align: "left"
                                        })
                                        .css({
                                            fontSize: '7px',
                                            textAlign:'left'
                                        })
                                        .add()
                                    }
                                    chart.bottomText.attr({
                                            x: leftb,
                                            y: topb
                                        });
                                if (!chart.customText) {
                                    chart.customText = chart.renderer
                                        .text(data.activeKiln + ' Kiln', left, top, true)
                                        .attr({
                                            align: "center"
                                        })
                                        .css({textAlign:'center',fontSize: '8px'})
                                        .addClass('customTitle').add();
                                }
                                chart.customText.attr({
                                    x: left,
                                    y: top
                                });
                            }
                        }
                    },
                    title: {
                        text: 'Real Summary',
                        align: 'left',
                        verticalAlign: 'top',
                        margin: 0,
                        padding: 0,
                        style: {
                            fontWeight: 'bold',
                            fontSize: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: null,
                        borderWidth: 0,
                        shadow: false,
                        pointFormat: '{point.percentage:.1f}%',
                        shared: true,
                        useHTML: true
                    },
                    legend: {
                        align: 'center',
                        borderWidth: 0,
                        margin:0,
                        lineHeight: 0,
                        padding:0,
                        labelFormatter:function () {
                            return this.name+':'+kFormatter(this.y);
                        },
                        itemStyle:{
                            fontSize: 7,
                            fontFamily:'Calibri',
                            fontWeight: 500,
                        },
                    },
                    plotOptions: {
                        pie: {
                            innerSize: "40%",
                            size:'100%',
                            enableMouseTracking: true,
                            dataLabels: {
                                enabled: true,
                                format: '{point.percentage:.1f} %',
                                distance: '-30%',
                                filter: {
                                    property: 'percentage',
                                    operator: '>',
                                    value: 4
                                },
                                style:{
                                    color:'#FFFFFF',
                                    textShadow: false,
                                    textOutline: "0px",
                                    fontSize: 7,
                                    fontWeight: 500,
                                },
                            },
                            showInLegend: true
                        }
                    },
                    colors: ['#66C8DE', '#0099CC', '#5A89A4', '#B4C7DA'],
                    series: [{
                        name: '',
                        colorByPoint: true,
                        data: [
                        {
                            name: "Stop Idle ",
                            y: parseFloat(data.real_summary_graph.stop_idle),
                        },{
                            name: "Op. H ",
                            y: parseFloat(data.real_summary_graph.oph),
                        },{
                            name:"UPDT ",
                            y: parseFloat(data.real_summary_graph.updt),
                        },{
                            name:"PDT ",
                            y:parseFloat(data.real_summary_graph.pdt) ,
                        }
                        ]
                    }]
                });
            },
            error: function(xmlhttprequest, textstatus, message) {

            }
        }).always(function() {
            $("#loader_real_summary").LoadingOverlay("hide");
        });
    }
    function rkapSummary() {
        $.ajax({
            type: "POST",
            url: "./rkap-summary-graph",
            dataType: 'JSON',
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            // timeout: 10000,
            beforeSend: function() {
                $("#loader_rkap_summary").LoadingOverlay("show");
            },
            success: function(data) {
                $("#loader_rkap_summary").LoadingOverlay("hide");

                $('#calTimeRKAP').empty()
                var time = kFormatter(parseInt(data.calTime)).toString()
                var chart2 = new Highcharts.Chart({
                    chart: {
                        style: {
                            fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                        },
                        type: "pie",
                        renderTo: "container_rkap_summary",
                        options:{
                            cutoutPercentage: 75,
                        },
                        events: {
                            render: function() {
                                var chart = this,
                                    rend = chart.renderer,
                                    pie = chart.series[0],
                                    left = chart.plotWidth / 2 + chart.plotLeft,
                                    top = chart.plotHeight / 2 + chart.plotTop;
                                    leftb = left+40;
                                    topb = top-80;
                                var labelText = '<span style="color: black">'+time+'<br/>' + '<span style="color: black">Cal. Time (hour)';
                                    if (!chart.bottomText) {
                                    chart.bottomText = chart.renderer.text(labelText, leftb, topb, true)
                                        .attr({
                                            zIndex: 5,
                                            align: "left"
                                        })
                                        .css({
                                            fontSize: '7px',
                                            textAlign:'left'
                                        })
                                        .add()
                                    }
                                    chart.bottomText.attr({
                                        x: leftb,
                                        y: topb
                                    });
                                if (!chart.customText) {
                                    chart.customText = chart.renderer
                                        .text(data.activeKiln + ' Kiln', left, top, true)
                                        .attr({
                                            align: "center"
                                        })
                                        .css({textAlign:'center',fontSize: '8px'})
                                        .addClass('customTitle').add();
                                }
                                chart.customText.attr({
                                    x: left,
                                    y: top
                                });
                            }
                        }
                    },
                    title: {
                        text: 'RKAP Summary',
                        align: 'left',
                        verticalAlign: 'top',
                        margin: 0,
                        padding: 0,
                        style: {
                            fontWeight: 'bold',
                            fontSize: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: null,
                        borderWidth: 0,
                        shadow: false,
                        pointFormat: '{point.percentage:.1f}%',
                        shared: true,
                        useHTML: true
                    },
                    legend: {
                        align: 'center',
                        borderWidth: 0,
                        margin:0,
                        lineHeight: 0,
                        padding:0,
                        labelFormatter:function () {
                            return this.name+':'+kFormatter(this.y);
                        },
                        itemStyle:{
                            fontSize: 7,
                            fontFamily:'Calibri',
                            fontWeight: 500,
                        },
                    },
                    plotOptions: {
                        pie: {
                            innerSize: "40%",
                            size:'100%',
                            enableMouseTracking: true,
                            dataLabels: {
                                enabled: true,
                                format: '{point.percentage:.1f} %',
                                distance: '-30%',
                                filter: {
                                    property: 'percentage',
                                    operator: '>',
                                    value: 4
                                },
                                style:{
                                    color:'#FFFFFF',
                                    textShadow: false,
                                    textOutline: "0px",
                                    fontSize: 7,
                                    fontWeight: 500,
                                },
                            },
                            showInLegend: true
                        }
                    },
                    colors: ['#66C8DE', '#0099CC', '#5A89A4', '#B4C7DA'],
                    series: [{
                        name: '',
                        colorByPoint: true,
                        data: [
                        {
                            name: "Stop Idle ",
                            y: parseFloat(data.rkap_summary_graph.stop_idle),
                        },{
                            name: "Op. H ",
                            y: parseFloat(data.rkap_summary_graph.oph),
                        },{
                            name:"UPDT ",
                            y: parseFloat(data.rkap_summary_graph.updt),
                        },{
                            name:"PDT ",
                            y:parseFloat(data.rkap_summary_graph.pdt) ,
                        }
                        ]
                    }]
                });
            },
            error: function(xmlhttprequest, textstatus, message) {
            }
        }).always(function() {
            $("#loader_rkap_summary").LoadingOverlay("hide");
        });
    }
    function rateSummary() {
        $.ajax({
            type: "POST",
            url: "./rate-summary-graph",
            dataType: 'JSON',
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            // timeout: 10000,
            beforeSend: function() {
                $("#loader_rate_summary").LoadingOverlay("show");
            },
            success: function(data) {
                $("#loader_rate_summary").LoadingOverlay("hide");
                $('#rateRKAP').empty()
                var time = kFormatter(parseInt(data.rateRKAP)).toString()
                var split = data.data.split("#");
                var total = split[0];
                var act_rate = split[1];
                var rate_updt = split[2];
                var rate_idle = split[3];
                var act_rate_total = split[4];
                var rate_updt_total = split[5];
                var rate_idle_total = split[6];

                var chart3 = new Highcharts.Chart({
                    chart: {
                        style: {
                            fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                        },
                        type: "pie",
                        renderTo: "container_rate_summary",
                        options:{
                            cutoutPercentage: 75,
                        },
                        events: {
                            render: function() {
                                var chart = this,
                                    rend = chart.renderer,
                                    pie = chart.series[0],
                                    left = chart.plotWidth / 2 + chart.plotLeft,
                                    top = chart.plotHeight / 2 + chart.plotTop;
                                    leftb = left+40;
                                    topb = top-75;
                                var labelText = '<span style="color: black">'+time+'<br/>' + '<span style="color: black">Rate Rkap(tpd)';
                                    if (!chart.bottomText) {
                                    chart.bottomText = chart.renderer.text(labelText, leftb, topb, true)
                                        .attr({
                                            zIndex: 5,
                                            align: "left"
                                        })
                                        .css({
                                            fontSize: '7px',
                                            textAlign:'left'
                                        })
                                        .add()
                                    }
                                    chart.bottomText.attr({
                                        x: leftb,
                                        y: topb
                                    });
                            }
                        }
                    },
                    title: {
                        text: 'Kiln Rate Summary',
                        align: 'left',
                        verticalAlign: 'top',
                        margin: 0,
                        padding: 0,
                        style: {
                            fontWeight: 'bold',
                            fontSize: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: null,
                        borderWidth: 0,
                        shadow: false,
                        pointFormat: '{point.percentage:.1f}%',
                        shared: true,
                        useHTML: true
                    },
                    legend: {
                        align: 'center',
                        borderWidth: 0,
                        margin:0,
                        lineHeight: 0,
                        padding:0,
                        labelFormatter:function () {
                            var total = 0,
                                percentage;

                            $.each(this.series.data, function() {
                                total += this.y;
                            });
                            percentage = ((this.y / total) * 100).toFixed(2);
                            return this.name+':'+kFormatter(percentage);
                        },
                        itemStyle:{
                            fontSize: 7,
                            fontFamily:'Calibri',
                            fontWeight: 500,
                        },
                    },
                    plotOptions: {
                        pie: {
                            innerSize: "40%",
                            size:'100%',
                            enableMouseTracking: true,
                            dataLabels: {
                                enabled: true,
                                format: '{point.percentage:.1f} %',
                                distance: '-30%',
                                filter: {
                                    property: 'percentage',
                                    operator: '>',
                                    value: 4
                                },
                                style:{
                                    color:'#FFFFFF',
                                    textShadow: false,
                                    textOutline: "0px",
                                    fontSize: 7,
                                    fontWeight: 500,
                                },
                            },
                            showInLegend: true
                        }
                    },
                    colors: ['#0099CC', '#66C8DE', '#5A89A4'],
                    series: [{
                        name: '',
                        colorByPoint: true,
                        data: [{
                            name: 'Act Rate ',
                            y: parseFloat(act_rate_total)
                        }, {
                            name: 'Lose Rate UPDT ',
                            y: parseFloat(rate_updt_total)
                        }, {
                            name: 'Lose Rate IDLE' ,
                            y: parseFloat(rate_idle_total)
                        }]
                    }]
                });
            },
            error: function(xmlhttprequest, textstatus, message) {

            }
        }).always(function() {
            $("#loader_rate_summary").LoadingOverlay("hide");
        });
    }

    function chartKilnNAI(colorReal, colorRKAP, fontSize) {
        $.ajax({
            type: "POST",
            url: "/kiln-operation-details-nai",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val(),
                tahun: $("#filter_data_tahun").val(),
                bulan: $("#filter_data_bulan").val(),
                tanggal: $("#filter_data_tanggal").val(),
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_kiln_opdt2").LoadingOverlay("show");
            },
            success: function(data) {
                var categories = []
                $.each(data.list_kode_plant, function(i, item) {
                    categories.push(item)
                });

                var data_nai_rkap = []
                $.each(data.data_nai, function(i, item) {
                    data_nai_rkap.push(item.nai_rkap)
                });

                var data_nai_real = []
                $.each(data.data_nai, function(i, item) {
                    data_nai_real.push(item.nai_real)
                });
                // start
                Highcharts.chart('kt_bargchart_3', {
                    chart: {
                        style: {
                            fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                        },
                    },
                    title: {
                        text:'Kiln Netto Availability',
                        align: 'left',
                        verticalAlign: 'top',
                        margin: 0,
                        padding: 0,
                        style: {
                            fontWeight: 'bold',
                            fontSize: 10
                        }
                    },
                    legend: {
                        align: 'left',
                        verticalAlign: 'top',
                        x: 0,
                        y: 0,
                        itemStyle: {
                            fontWeight: 500,
                            fontSize: 10
                        }
                    },
                    plotOptions: {
                        line: {
                            dataLabels: {
                                allowOverlap: true,
                                enabled: true
                            }
                        },
                        series:{
                            pointWidth: 21,
                            pointPadding: 0,
                            groupPadding: 0.1,
                            borderWidth: 0,
                            shadow: false
                        }
                    },
                    yAxis: {
                        title: {
                            text: ''
                        },
                        endOnTick: false,
                        tickAmount: 3,
                        labels: {
                            formatter: function () {
                                return this.axis.defaultLabelFormatter.call(this)+'%';
                            },
                            style:{
                                fontSize: 7,
                                fontFamily: 'Calibri'
                            }
                        }
                    },
                    xAxis: {
                        categories,
                        labels:{
                            rotation:-90,
                            style:{
                                fontSize: 7,
                                fontFamily: 'Calibri'
                            }
                        }
                    },
                    series: [{
                            name: 'NAI Real (%)',
                            color: colorReal,
                            type: 'column',
                            data: data_nai_real
                        },
                        {
                            name: 'NAI RKAP (%)',
                            color: colorRKAP,
                            type: 'spline',
                            data: data_nai_rkap
                        }
                    ]
                });
            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_kiln_opdt2").LoadingOverlay("hide");
        })
    }
    function chartKilnRATE(colorOPH, colorReal, colorRKAP, fontSize) {
        $.ajax({
            type: "POST",
            url: "./get-kiln-rate",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#kiln_rate_overlay").LoadingOverlay("show");
            },
            success: function(data) {

                var categories = []
                $.each(data.list_kode_plant, function(i, item) {
                    categories.push(item)
                });

                var rate_gross_real = []
                $.each(data.kiln_rate, function(i, item) {
                    rate_gross_real.push(item.rate_gross_real)
                });

                var rate_gross_rkap = []
                $.each(data.kiln_rate, function(i, item) {
                    rate_gross_rkap.push(item.rate_gross_rkap)
                });

                var rate_netto_real = []
                $.each(data.kiln_rate, function(i, item) {
                    rate_netto_real.push(item.rate_netto_real)
                });
                Highcharts.chart('kiln_rate_chart', {
                    chart: {
                        style: {
                            fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                        },
                    },
                    title: {
                        text:'Kiln Rate',
                        align: 'left',
                        verticalAlign: 'top',
                        margin: 0,
                        padding: 0,
                        style: {
                            fontWeight: 'bold',
                            fontSize: 10
                        }
                    },
                    legend: {
                        align: 'left',
                        verticalAlign: 'top',
                        x: 0,
                        y: 0,
                        itemStyle: {
                            fontWeight: 500,
                            fontSize: 10
                        }
                    },
                    plotOptions: {
                        line: {
                            dataLabels: {
                                allowOverlap: true,
                                enabled: true
                            }
                        },
                        series:{
                            pointWidth:10,
                            pointPadding: 0,
                            groupPadding: 0.1,
                            borderWidth: 0,
                            shadow: false
                        }
                    },
                    yAxis: {
                        title: {
                            text: ''
                        },
                        labels:{
                            style:{
                                fontSize: 7,
                                fontFamily: 'Calibri'
                            }
                        },
                        endOnTick: false,
                        tickAmount: 3,
                    },
                    xAxis: {
                        categories: categories,
                        labels:{
                            rotation:-90,
                            style:{
                                fontSize: 7,
                                fontFamily: 'Calibri'
                            }
                        }
                    },
                    series: [{
                            name: 'Rate Gross Real (TPD)',
                            color: colorOPH,
                            type: 'column',
                            data: rate_gross_real
                        },
                        {
                            name: 'Rate Netto Real (TPD)',
                            color: colorReal,
                            type: 'column',
                            data: rate_netto_real
                        },
                        {
                            name: 'Rate Gross RKAP (TPD)',
                            color: colorRKAP,
                            type: 'spline',
                            data: rate_gross_rkap
                        }
                    ]
                });
            },
            error: function(xmlhttprequest, textstatus, message) {}

        }).always(function() {
            $("#kiln_rate_overlay").LoadingOverlay("hide");
        });

    }

    function chartKilnGrosOEE(colorReal, colorRKAP, fontSize) {
        $.ajax({
            type: "POST",
            url: "/get-kiln-gros-oee",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val(),
                tahun: $("#filter_data_tahun").val(),
                bulan: $("#filter_data_bulan").val(),
                tanggal: $("#filter_data_tanggal").val(),
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#kiln_gros_oee_overlay").LoadingOverlay("show");
            },
            success: function(data) {
                var categories = []
                $.each(data.list_kode_plant, function(i, item) {
                    categories.push(item)
                });

                var oee_gross_real = []
                $.each(data.oee_gross, function(i, item) {
                    oee_gross_real.push(item.oee_gross_real)
                });

                var oee_gross_rkap = []
                $.each(data.oee_gross, function(i, item) {
                    oee_gross_rkap.push(item.oee_gross_rkap)
                });
                Highcharts.chart('kiln_gros_oee', {
                    chart: {
                        style: {
                            fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                        },
                    },
                    title: {
                        text:'Kiln Gross OEE',
                        align: 'left',
                        verticalAlign: 'top',
                        margin: 0,
                        padding: 0,
                        style: {
                            fontWeight: 'bold',
                            fontSize: 10
                        }
                    },
                    legend: {
                        align: 'left',
                        verticalAlign: 'top',
                        x: 0,
                        y: 0,
                        itemStyle: {
                            fontWeight: 500,
                            fontSize: 10
                        }
                    },
                    plotOptions: {
                        line: {
                            dataLabels: {
                                allowOverlap: true,
                                enabled: true
                            }
                        },
                        series:{
                            pointWidth: 21,
                            pointPadding: 0,
                            groupPadding: 0.1,
                            borderWidth: 0,
                            shadow: false
                        }
                    },
                    yAxis: {
                        title: {
                            text: ''
                        },
                        endOnTick: false,
                        tickAmount: 3,
                        labels: {
                            formatter: function() {
                                return this.axis.defaultLabelFormatter.call(this) + '%';
                            },
                            style:{
                                fontSize: 7,
                                fontFamily: "Calibri"
                            }
                        }
                    },
                    xAxis: {
                        categories: categories,
                        labels:{
                            rotation:-90,
                            style:{
                                fontSize: 7,
                                fontFamily: 'Calibri'
                            }
                        }
                    },
                    series: [{
                            name: 'OEE Gross Real (%)',
                            color: colorReal,
                            type: 'column',
                            data: oee_gross_real,

                        },
                        {
                            name: 'OEE Gross Rkap (%)',
                            color: colorRKAP,
                            type: 'spline',
                            data: oee_gross_rkap,
                        }
                    ]
                });
            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#kiln_gros_oee_overlay").LoadingOverlay("hide");
        });
    }
    function chartKilnNettoOEE() {
        // start
        $.ajax({
            type: "POST",
            url: "/kiln-netto-oee",
            data: {
                opco: $("#filter_data_opco").val(),
                plant: $("#filter_data_plant").val()
            },
            dataType: 'JSON',
            beforeSend: function() {
                $("#loader_kiln_oee").LoadingOverlay("show");
            },
            success: function(data) {
                var categories = []
                var result = []
                $.each(data.dataSort, function(i, item) {
                    categories.push(item.key)
                    result.push({
                        y: item.data,
                        color: item.color
                    })
                });
                Highcharts.chart('kiln_netto_oee', {
                    chart: {
                        style: {
                            fontFamily: 'Calibri, Poppins, Helvetica, "sans-serif"',
                        },
                    },
                    title: {
                        text:'Kiln Netto OEE',
                        align: 'left',
                        verticalAlign: 'top',
                        style: {
                            fontWeight: 'bold',
                            fontSize: 10
                        }
                    },
                    legend: {
                        enabled: false
                    },
                    plotOptions: {
                        line: {
                            dataLabels: {
                                allowOverlap: true,
                                enabled: true
                            }
                        },
                        series:{
                            pointWidth: 21,
                            pointPadding: 0,
                            groupPadding: 0.1,
                            borderWidth: 0,
                            shadow: false
                        }
                    },
                    yAxis: {
                        title: {
                            text: ''
                        },
                        endOnTick: false,
                        tickAmount: 3,
                        labels: {
                            formatter: function () {
                                return this.axis.defaultLabelFormatter.call(this)+'%';
                            },
                            style:{
                                fontSize: 7,
                                fontFamily: "Calibri"
                            }
                        }
                    },
                    xAxis: {
                        categories,
                        labels:{
                            rotation:-90,
                            style:{
                                fontSize: 7,
                                fontFamily: 'Calibri'
                            }
                        }
                    },
                    series: [{
                        name: 'OEE Netto Real (%)',
                        color: '#0B70C7',
                        type: 'column',
                        data: result,
                    }]
                });

            },
            error: function(xmlhttprequest, textstatus, message) {}
        }).always(function() {
            $("#loader_kiln_oee").LoadingOverlay("hide");
        });
    }

    function kFormatter(num) {
        return Math.abs(num) > 999 ? Math.sign(num)*((Math.abs(num)/1000).toFixed(2)) + 'K' : (Math.sign(num)*Math.abs(num)).toFixed(1)
    }
</script>


<script>
    /**
     * Zoom out the page when in fullscreen mode.
     * Ref: https://stackoverflow.com/a/59413539
     */

    // const sidebar = $('#kt_aside');
    // const contentWrapper = $('#kt_wrapper');

    // const oldPaddingLeft = contentWrapper.css('padding-left');

    function isFullscreen() {
        return 1 >= outerHeight - innerHeight;
    };

    function handleFullscreenChange() {
        let scaleAmount;

        if (isFullscreen()) {
            scaleAmount = 0.75;
            // sidebar.attr('style', 'display: none !important');
            // contentWrapper.css('padding-left', '0');
        } else {
            scaleAmount = 1;
            // sidebar.attr('style', 'display: flex !important');
            // contentWrapper.css('padding-left', oldPaddingLeft);
        }

        // Ref: https://stackoverflow.com/a/15489890/12570847
        const isFirefox = navigator.userAgent.indexOf('Firefox') != -1 && parseFloat(navigator.userAgent.substring(navigator.userAgent.indexOf('Firefox') + 8)) >= 3.6

        if (isFirefox) {
            $(document).css('MozTransform', `scale(${scaleAmount})`);
            $(document).css('width', `${100 * (1 / scaleAmount)}%`);
            $(document).css('transform-origin', '0 0');
        } else {
            document.body.style.zoom = scaleAmount;
        }
    }

    window.addEventListener('resize', handleFullscreenChange);

    handleFullscreenChange();
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\dev-dmm\resources\views/PerformanceRealtime.blade.php ENDPATH**/ ?>