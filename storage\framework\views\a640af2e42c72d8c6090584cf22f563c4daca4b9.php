<?php
    $isPerformRealtimePage = Request::is('perform-realtime');
    $isPerformHistoryPage = Request::is('perform-history');

    if(Request::is('perform-realtime'))         
        $filterDashboard = Request::is('perform-realtime');    
    elseif(Request::is('perform-history'))
        $filterDashboard = Request::is('perform-history'); 

?>

<style>
    .btn.btn-filter-apply {
        border-color: #5d5d5d;
        color: #5d5d5d
    }

    .btn.btn-filter-apply:hover {
        background-color: #5d5d5d;
        color: #fff;
    }

    .btn.btn-filter-history {
        background-color: #931F26;
        color: #fff;
    }

    .btn.btn-filter-history:hover {
        background-color: #f3d4d4;
        color: #9E2A2B;
        border: solid 1px #9E2A2B;
    }

    .text-red{
        color: #9E2A2B;
    }
</style>

<div id="kt_header" class="bg-header header header-fixed" style="left:0;margin-left: 0px !important;margin-right: 0px !important; height:50px">
    <!--begin::Container-->
    <div class="container-fluid d-flex align-items-stretch justify-content-between">
        <div id="kt_subheader" class="w-100">
            <div class="container-fluid d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap w-100">
                <div class="row align-items-center pt-2 w-100">
                    <?php if(isset($filterDashboard)): ?>
                        <?php if($isPerformHistoryPage): ?>
                            <div class="col-md-4 px-0 d-flex">
                        <?php else: ?>
                            <div class="col-md-6 px-0 d-flex">
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="col-md-11 px-0 d-flex">
                    <?php endif; ?>
                        <button class="brand-toggle btn btn-sm px-0 mr-3" id="kt_navbar_toggle">
                            <img src="../assets/media/svg/icons/menu-sidebar.svg" alt="">
                        </button>
                        <div class="row ml-1">
                            <div class="col-lg-12 col-md-12 px-0">
                                <div class="d-flex align-items-center flex-wrap ">
                                    <h3 class="text-dark font-weight-bold mb-0" style='font-weight:bold !important'><?php echo e($title ?? ''); ?></h3>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 px-0">
                                <div class="d-flex align-items-center flex-wrap">
                                    <!--begin::Page Title-->

                                    <!--end::Page Title-->
                                    <!--begin::Actions-->
                                    <?php if(isset($breadcrumb)): ?>
                                        <?php $__currentLoopData = $breadcrumb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $br): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($br['url']): ?>
                                                <div class="">
                                                    <a class="text-muted font-weight-bold" href="<?php echo e($br['url']); ?>"><?php echo e($br['title']); ?> &nbsp;</a>
                                                </div>
                                            <?php else: ?>
                                                <div class="text-dark mx-1">/</div>
                                                <div class="text-red font-weight-bold "><?php echo e($br['title']); ?> &nbsp;</div>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                    <!--end::Actions-->
                                </div>
                            </div>
                        </div>
                        <button class="brand-toggle btn btn-sm px-0 ml-auto" id="kt_filter" style="margin-right: -30px;">
                            <i class="fa fa-filter" aria-hidden="true" style="font-size:20px !important;color:black"></i>
                        </button>
                    </div>

                     <!--begin::Filter Menu Perform Realtime-->
                    <?php if($isPerformRealtimePage): ?>
                        <div class="col-md-5 px-0 filter-top mobile-filter">
                            <div class="d-flex justify-content-end align-items-center flex-no-wrap h-100">
                                <div id="loader_opco" class="flex-grow-1" style="max-width:130px">
                                    <select id="filter_data_opco" name="filter_data_opco" class="select2 wd-100 form-control form-control-lg" style="width:100%;">
                                        <option value="" selected>All Opco</option>
                                    </select>
                                </div>
                                <div id="loader_plant" class="flex-grow-1 ml-1" style="max-width:130px">
                                    <select id="filter_data_plant" name="filter_data_plant" class="select2 wd-100 form-control form-control-lg" style="width:100%;">
                                        <option value="" selected>All Plant</option>
                                    </select>
                                </div>
                                <button type="button" onclick="changeFilterData();" class="btn btn-filter-apply btn-sm ml-1">
                                    Apply
                                </button>
                                <a href="/perform-history" type="button" class="btn btn-filter-history btn-sm ml-1">History</a>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if($isPerformHistoryPage): ?>
                        <div class="col-md-7 px-0 filter-top mobile-filter">
                            <div class="d-flex justify-content-end align-items-center flex-no-wrap h-100">
                                <div id="loader_opco2" class="flex-grow-1" style="max-width:130px">
                                    <select id="filter_data_opco" name="filter_data_opco" class="select2 wd-100 form-control form-control-lg" style="width:100%;">
                                        <option value="" selected>All Opco</option>
                                    </select>
                                </div>
                                <div id="loader_plant" class="flex-grow-1 ml-1" style="max-width:130px">
                                    <select id="filter_data_plant" name="filter_data_plant" class="select2 wd-100 form-control form-control-lg" style="width:100%;">
                                        <option value="" selected>All Plant</option>
                                    </select>
                                </div>
                                <div class="flex-grow-1 ml-1" style="max-width:130px" id="loader_tahun">
                                    <select id="filter_data_tahun" name="filter_data_tahun" class="select2 wd-100 form-control form-control-lg" style="width:100%;">
                                    </select>
                                </div>
                                <div class="flex-grow-1 ml-1" style="max-width:130px" id="loader_bulan">
                                    <select id="filter_data_bulan" name="filter_data_bulan" class="wd-100 form-control form-control-lg" style="width:100%;" multiple="multiple">
                                    </select>
                                </div> 
                                <button type="button" onclick="changeFilterData();" class="btn btn-filter-apply btn-sm ml-1">
                                    Apply
                                </button>
                                <a href="/perform-realtime" type="button" class="btn btn-filter-history btn-sm ml-1">Realtime</a>
                                <!-- added by rama 30 may 2023 exportFR -->
                                <a onclick="getExportFR()" class="btn btn-danger btn-sm ml-1"><i
                                    class="fa-solid fa-file-arrow-down"></i>Export FR</a>
                                <button type="button" class="btn btn-danger btn-sm ml-1 dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa-solid fa-file-arrow-down"></i>
                                    <span>Export MTC Cost</span>
                                </button>
                                <ul class="dropdown-menu w-20" >
                                    <li><button class="dropdown-item" onclick="getExportMTCCapex()">Capex</button></li>
                                    <li><button class="dropdown-item" onclick="getExportMTC()">Opex</button></li>
                                </ul>
                                <!-- end of added by rama -->
                            </div>
                        </div>
                    <?php endif; ?>
                    <!--end::Filter Menu Perform Realtime-->
                    <div class="col-md-1 px-0 filter-top text-right">
                        <div class="row">
                            <div class="col-11">
                                <span id="time-now">time</span>
                                <span id ="date-now"></span>
                            </div>
                            <div class="col-1 px-0 d-flex align-items-center">
                                <img src="../assets/media/svg/icons/calendar-time.svg" alt="">
                            </div>
                        </div>
                    </div>
                </div>
                <!--begin::Info-->

                <!--end::Info-->
            </div>
        </div>
        <!--begin::Header Menu Wrapper-->
        <div class="header-menu-wrapper header-menu-wrapper-left" id="kt_header_menu_wrapper">
            <!--begin::Header Menu-->
            <div id="kt_header_menu" class="header-menu header-menu-mobile header-menu-layout-default">
                <!--begin::Header Nav-->
                <a href="#" class="brand-logo">
                    
                </a>
                <!--end::Header Nav-->
            </div>
            <!--end::Header Menu-->
        </div>
        <!--end::Header Menu Wrapper-->

    </div>
    <!--end::Container-->
</div>
<?php /**PATH C:\laragon\www\dev-dmm\resources\views/layouts/header.blade.php ENDPATH**/ ?>